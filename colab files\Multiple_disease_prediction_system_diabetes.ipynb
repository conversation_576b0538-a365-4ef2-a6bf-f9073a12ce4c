{"cells": [{"cell_type": "markdown", "metadata": {"id": "LnPbntVRnfvV"}, "source": ["Importing the Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-71UtHzNVWjB"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn import svm\n", "from sklearn.metrics import accuracy_score"]}, {"cell_type": "markdown", "metadata": {"id": "bmfOfG8joBBy"}, "source": ["Data Collection and Analysis\n", "\n", "PIMA Diabetes Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Xpw6Mj_pn_TL"}, "outputs": [], "source": ["# loading the diabetes dataset to a pandas DataFrame\n", "diabetes_dataset = pd.read_csv('/content/diabetes.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "-tjO09ncovoh", "outputId": "a58d3b72-392b-45c1-dd0f-eafeb46932d7"}, "outputs": [{"data": {"text/html": ["\n", "  <div id=\"df-93f5d842-c97e-4561-be8f-fcbf6ac70054\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pregnancies</th>\n", "      <th>Glucose</th>\n", "      <th>BloodPressure</th>\n", "      <th>SkinThickness</th>\n", "      <th>Insulin</th>\n", "      <th>BMI</th>\n", "      <th>DiabetesPedigreeFunction</th>\n", "      <th>Age</th>\n", "      <th>Outcome</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6</td>\n", "      <td>148</td>\n", "      <td>72</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>33.6</td>\n", "      <td>0.627</td>\n", "      <td>50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>85</td>\n", "      <td>66</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>26.6</td>\n", "      <td>0.351</td>\n", "      <td>31</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>183</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>23.3</td>\n", "      <td>0.672</td>\n", "      <td>32</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>89</td>\n", "      <td>66</td>\n", "      <td>23</td>\n", "      <td>94</td>\n", "      <td>28.1</td>\n", "      <td>0.167</td>\n", "      <td>21</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>137</td>\n", "      <td>40</td>\n", "      <td>35</td>\n", "      <td>168</td>\n", "      <td>43.1</td>\n", "      <td>2.288</td>\n", "      <td>33</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-93f5d842-c97e-4561-be8f-fcbf6ac70054')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-93f5d842-c97e-4561-be8f-fcbf6ac70054 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-93f5d842-c97e-4561-be8f-fcbf6ac70054');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "], "text/plain": ["   Pregnancies  Glucose  BloodPressure  SkinThickness  Insulin   BMI  \\\n", "0            6      148             72             35        0  33.6   \n", "1            1       85             66             29        0  26.6   \n", "2            8      183             64              0        0  23.3   \n", "3            1       89             66             23       94  28.1   \n", "4            0      137             40             35      168  43.1   \n", "\n", "   DiabetesPedigreeFunction  Age  Outcome  \n", "0                     0.627   50        1  \n", "1                     0.351   31        0  \n", "2                     0.672   32        1  \n", "3                     0.167   21        0  \n", "4                     2.288   33        1  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# printing the first 5 rows of the dataset\n", "diabetes_dataset.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lynParo6pEMB", "outputId": "122cca56-ad67-4824-8339-f5b1dd062c71"}, "outputs": [{"data": {"text/plain": ["(768, 9)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# number of rows and Columns in this dataset\n", "diabetes_dataset.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 300}, "id": "3NDJOlrEpmoL", "outputId": "64bd6965-4a02-4fd4-f6ee-011d77f86ea3"}, "outputs": [{"data": {"text/html": ["\n", "  <div id=\"df-5700d088-6a6d-4b0b-8fe4-6e29693af24a\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pregnancies</th>\n", "      <th>Glucose</th>\n", "      <th>BloodPressure</th>\n", "      <th>SkinThickness</th>\n", "      <th>Insulin</th>\n", "      <th>BMI</th>\n", "      <th>DiabetesPedigreeFunction</th>\n", "      <th>Age</th>\n", "      <th>Outcome</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "      <td>768.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3.845052</td>\n", "      <td>120.894531</td>\n", "      <td>69.105469</td>\n", "      <td>20.536458</td>\n", "      <td>79.799479</td>\n", "      <td>31.992578</td>\n", "      <td>0.471876</td>\n", "      <td>33.240885</td>\n", "      <td>0.348958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.369578</td>\n", "      <td>31.972618</td>\n", "      <td>19.355807</td>\n", "      <td>15.952218</td>\n", "      <td>115.244002</td>\n", "      <td>7.884160</td>\n", "      <td>0.331329</td>\n", "      <td>11.760232</td>\n", "      <td>0.476951</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.078000</td>\n", "      <td>21.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.000000</td>\n", "      <td>99.000000</td>\n", "      <td>62.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>27.300000</td>\n", "      <td>0.243750</td>\n", "      <td>24.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.000000</td>\n", "      <td>117.000000</td>\n", "      <td>72.000000</td>\n", "      <td>23.000000</td>\n", "      <td>30.500000</td>\n", "      <td>32.000000</td>\n", "      <td>0.372500</td>\n", "      <td>29.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>6.000000</td>\n", "      <td>140.250000</td>\n", "      <td>80.000000</td>\n", "      <td>32.000000</td>\n", "      <td>127.250000</td>\n", "      <td>36.600000</td>\n", "      <td>0.626250</td>\n", "      <td>41.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>17.000000</td>\n", "      <td>199.000000</td>\n", "      <td>122.000000</td>\n", "      <td>99.000000</td>\n", "      <td>846.000000</td>\n", "      <td>67.100000</td>\n", "      <td>2.420000</td>\n", "      <td>81.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-5700d088-6a6d-4b0b-8fe4-6e29693af24a')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-5700d088-6a6d-4b0b-8fe4-6e29693af24a button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-5700d088-6a6d-4b0b-8fe4-6e29693af24a');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "], "text/plain": ["       Pregnancies     Glucose  BloodPressure  SkinThickness     Insulin  \\\n", "count   768.000000  768.000000     768.000000     768.000000  768.000000   \n", "mean      3.845052  120.894531      69.105469      20.536458   79.799479   \n", "std       3.369578   31.972618      19.355807      15.952218  115.244002   \n", "min       0.000000    0.000000       0.000000       0.000000    0.000000   \n", "25%       1.000000   99.000000      62.000000       0.000000    0.000000   \n", "50%       3.000000  117.000000      72.000000      23.000000   30.500000   \n", "75%       6.000000  140.250000      80.000000      32.000000  127.250000   \n", "max      17.000000  199.000000     122.000000      99.000000  846.000000   \n", "\n", "              BMI  DiabetesPedigreeFunction         Age     Outcome  \n", "count  768.000000                768.000000  768.000000  768.000000  \n", "mean    31.992578                  0.471876   33.240885    0.348958  \n", "std      7.884160                  0.331329   11.760232    0.476951  \n", "min      0.000000                  0.078000   21.000000    0.000000  \n", "25%     27.300000                  0.243750   24.000000    0.000000  \n", "50%     32.000000                  0.372500   29.000000    0.000000  \n", "75%     36.600000                  0.626250   41.000000    1.000000  \n", "max     67.100000                  2.420000   81.000000    1.000000  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# getting the statistical measures of the data\n", "diabetes_dataset.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LrpHzaGpp5dQ", "outputId": "71928935-3a8e-4bc3-9b5e-536ae570ac5c"}, "outputs": [{"data": {"text/plain": ["0    500\n", "1    268\n", "Name: Outcome, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["diabetes_dataset['Outcome'].value_counts()"]}, {"cell_type": "markdown", "metadata": {"id": "cB1qRaNcqeh5"}, "source": ["0 --> Non-Diabetic\n", "\n", "1 --> Diabetic"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "I6MWR0k_qSCK", "outputId": "08da2ed6-1009-49bf-838b-86453a250b20"}, "outputs": [{"data": {"text/html": ["\n", "  <div id=\"df-e421e310-2da5-4262-8a4c-12e187c396b6\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pregnancies</th>\n", "      <th>Glucose</th>\n", "      <th>BloodPressure</th>\n", "      <th>SkinThickness</th>\n", "      <th>Insulin</th>\n", "      <th>BMI</th>\n", "      <th>DiabetesPedigreeFunction</th>\n", "      <th>Age</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Outcome</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3.298000</td>\n", "      <td>109.980000</td>\n", "      <td>68.184000</td>\n", "      <td>19.664000</td>\n", "      <td>68.792000</td>\n", "      <td>30.304200</td>\n", "      <td>0.429734</td>\n", "      <td>31.190000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.865672</td>\n", "      <td>141.257463</td>\n", "      <td>70.824627</td>\n", "      <td>22.164179</td>\n", "      <td>100.335821</td>\n", "      <td>35.142537</td>\n", "      <td>0.550500</td>\n", "      <td>37.067164</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-e421e310-2da5-4262-8a4c-12e187c396b6')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-e421e310-2da5-4262-8a4c-12e187c396b6 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-e421e310-2da5-4262-8a4c-12e187c396b6');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "], "text/plain": ["         Pregnancies     Glucose  BloodPressure  SkinThickness     Insulin  \\\n", "Outcome                                                                      \n", "0           3.298000  109.980000      68.184000      19.664000   68.792000   \n", "1           4.865672  141.257463      70.824627      22.164179  100.335821   \n", "\n", "               BMI  DiabetesPedigreeFunction        Age  \n", "Outcome                                                  \n", "0        30.304200                  0.429734  31.190000  \n", "1        35.142537                  0.550500  37.067164  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["diabetes_dataset.groupby('Outcome').mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RoDW7l9mqqHZ"}, "outputs": [], "source": ["# separating the data and labels\n", "X = diabetes_dataset.drop(columns = 'Outcome', axis=1)\n", "Y = diabetes_dataset['Outcome']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3eiRW9M9raMm", "outputId": "a6af1bfd-3e31-4b63-8ebf-dd817873a816"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     Pregnancies  Glucose  BloodPressure  SkinThickness  Insulin   BMI  \\\n", "0              6      148             72             35        0  33.6   \n", "1              1       85             66             29        0  26.6   \n", "2              8      183             64              0        0  23.3   \n", "3              1       89             66             23       94  28.1   \n", "4              0      137             40             35      168  43.1   \n", "..           ...      ...            ...            ...      ...   ...   \n", "763           10      101             76             48      180  32.9   \n", "764            2      122             70             27        0  36.8   \n", "765            5      121             72             23      112  26.2   \n", "766            1      126             60              0        0  30.1   \n", "767            1       93             70             31        0  30.4   \n", "\n", "     DiabetesPedigreeFunction  Age  \n", "0                       0.627   50  \n", "1                       0.351   31  \n", "2                       0.672   32  \n", "3                       0.167   21  \n", "4                       2.288   33  \n", "..                        ...  ...  \n", "763                     0.171   63  \n", "764                     0.340   27  \n", "765                     0.245   30  \n", "766                     0.349   47  \n", "767                     0.315   23  \n", "\n", "[768 rows x 8 columns]\n"]}], "source": ["print(X)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AoxgTJAMrcCl", "outputId": "b0c8e0e3-84c9-4fc7-fdd0-7eb632ade0e8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0      1\n", "1      0\n", "2      1\n", "3      0\n", "4      1\n", "      ..\n", "763    0\n", "764    0\n", "765    0\n", "766    1\n", "767    0\n", "Name: Outcome, Length: 768, dtype: int64\n"]}], "source": ["print(Y)"]}, {"cell_type": "markdown", "metadata": {"id": "gHciEFkxsoQP"}, "source": ["Train Test Split"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AEfKGj_yslvD"}, "outputs": [], "source": ["X_train, X_test, Y_train, Y_test = train_test_split(X,Y, test_size = 0.2, stratify=Y, random_state=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DR05T-o0t3FQ", "outputId": "5fc323cc-5eaf-48ba-d3ea-041df12b7e9c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(768, 8) (614, 8) (154, 8)\n"]}], "source": ["print(X.shape, X_train.shape, X_test.shape)"]}, {"cell_type": "markdown", "metadata": {"id": "ElJ3tkOtuC_n"}, "source": ["Training the Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5szLWHlNt9xc"}, "outputs": [], "source": ["classifier = svm.SVC(kernel='linear')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 74}, "id": "ncJWY_7suPAb", "outputId": "a8a7031b-1a48-4d38-ef42-d40a44acbce2"}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {color: black;background-color: white;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>SVC(kernel=&#x27;linear&#x27;)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">SVC</label><div class=\"sk-toggleable__content\"><pre>SVC(kernel=&#x27;linear&#x27;)</pre></div></div></div></div></div>"], "text/plain": ["SVC(kernel='linear')"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["#training the support vector Machine Classifier\n", "classifier.fit(X_train, Y_train)"]}, {"cell_type": "markdown", "metadata": {"id": "UV4-CAfquiyP"}, "source": ["Model Evaluation"]}, {"cell_type": "markdown", "metadata": {"id": "yhAjGPJWunXa"}, "source": ["Accuracy Score"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fJLEPQK7ueXp"}, "outputs": [], "source": ["# accuracy score on the training data\n", "X_train_prediction = classifier.predict(X_train)\n", "training_data_accuracy = accuracy_score(X_train_prediction, Y_train)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mmJ22qhVvNwj", "outputId": "67103fcb-9f76-4a9d-9e2d-35cc5a604543"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy score of the training data :  0.7833876221498371\n"]}], "source": ["print('Accuracy score of the training data : ', training_data_accuracy)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "G2CICFMEvcCl"}, "outputs": [], "source": ["# accuracy score on the test data\n", "X_test_prediction = classifier.predict(X_test)\n", "test_data_accuracy = accuracy_score(X_test_prediction, Y_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "i2GcW_t_vz7C", "outputId": "1a49c2aa-ed9d-466f-8dda-d0b6b06a456d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy score of the test data :  0.7727272727272727\n"]}], "source": ["print('Accuracy score of the test data : ', test_data_accuracy)"]}, {"cell_type": "markdown", "metadata": {"id": "gq8ZX1xpwPF5"}, "source": ["Making a Predictive System"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U-ULRe4yv5tH", "outputId": "7ebf045a-d0a6-4b47-f42d-2b6abab0b40c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1]\n", "The person is diabetic\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/base.py:439: UserWarning: X does not have valid feature names, but SVC was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["input_data = (5,166,72,19,175,25.8,0.587,51)\n", "\n", "# changing the input_data to numpy array\n", "input_data_as_numpy_array = np.asarray(input_data)\n", "\n", "# reshape the array as we are predicting for one instance\n", "input_data_reshaped = input_data_as_numpy_array.reshape(1,-1)\n", "\n", "prediction = classifier.predict(input_data_reshaped)\n", "print(prediction)\n", "\n", "if (prediction[0] == 0):\n", "  print('The person is not diabetic')\n", "else:\n", "  print('The person is diabetic')"]}, {"cell_type": "markdown", "metadata": {"id": "FCHCMHpshHU4"}, "source": ["Saving the trained model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cdmTOR4MhHCB"}, "outputs": [], "source": ["import pickle"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4gN09lokhKuZ"}, "outputs": [], "source": ["filename = 'diabetes_model.sav'\n", "pickle.dump(classifier, open(filename, 'wb'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IKW4D5CqhP5X"}, "outputs": [], "source": ["# loading the saved model\n", "loaded_model = pickle.load(open('diabetes_model.sav', 'rb'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "exbg9-VWiHRx", "outputId": "2ef27849-ff09-4939-bb98-37d1f2c25cdd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1]\n", "The person is diabetic\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/base.py:439: UserWarning: X does not have valid feature names, but SVC was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["input_data = (5,166,72,19,175,25.8,0.587,51)\n", "\n", "# changing the input_data to numpy array\n", "input_data_as_numpy_array = np.asarray(input_data)\n", "\n", "# reshape the array as we are predicting for one instance\n", "input_data_reshaped = input_data_as_numpy_array.reshape(1,-1)\n", "\n", "prediction = loaded_model.predict(input_data_reshaped)\n", "print(prediction)\n", "\n", "if (prediction[0] == 0):\n", "  print('The person is not diabetic')\n", "else:\n", "  print('The person is diabetic')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IP-TYuEFOTF4", "outputId": "7f36e15b-a92f-410f-86e6-cdfb3958eb69"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pregnancies\n", "Glucose\n", "BloodPressure\n", "SkinThickness\n", "<PERSON><PERSON><PERSON>\n", "BMI\n", "DiabetesPedigreeFunction\n", "Age\n"]}], "source": ["for column in X.columns:\n", "  print(column)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}