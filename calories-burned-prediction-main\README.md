# calories-burned-prediction

[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/drive/1VT-pzbDFZtnUE01hv1LjrVR6pyTDkyr9)

With this [application](https://calories-burned-prediction.herokuapp.com) you will be able to evaluate how many calories you would burn during a specific duration.This app takes some parameters such as your `age` , `gender` , `exercise duration` etc. and then this app evaluates the amount of calories you would burn.In addition, you will be able to observe similar results and general informations(according to the parameter values that you would enter into application).  

This project is made up of a simple machine learning algorithm(`RandomForestRegressor`) from `sklearn` library.Various concepts implemented in this project such as `EDA` , `[Pearson] Correlation` , `Learning Curve` etc. which is available in Jupyter/Colab notebook.

# Running streamlit file

After downloading and saving the streamlit file (`calories-prediction-streamlit.py`) into a certain directory, you have to open `Command Prompt`.In your command prompt change the directory to the directory where the streamlit file is saved and then type:

```
streamlit run calories-prediction-streamlit.py
```

After couple of seconds the WebApp pops up in your browser and then you can modify it with your prefered Interpreter.For saving changes on the WebApp and streamlit file you have to press `Ctrl + S` while you are into your interpreter.

# Reporting bugs and issues

If you have any bugs or issues with this app, please state that in the `Issues` section of this Repository.In addition to that, you can keep in touch with myself by this Gmail:\
<EMAIL>
