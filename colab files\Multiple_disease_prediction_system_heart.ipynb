{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "aTb-9TFFqprC"}, "source": ["Importing the Dependencies"]}, {"cell_type": "code", "metadata": {"id": "3q9U3S_whh3-"}, "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score"], "execution_count": 1, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "egMd5zeurTMR"}, "source": ["Data Collection and Processing"]}, {"cell_type": "code", "metadata": {"id": "0q-3-LkQrREV", "colab": {"base_uri": "https://localhost:8080/", "height": 345}, "outputId": "1463aeb7-664e-4bc2-9f24-2a409698bccb"}, "source": ["# loading the csv data to a Pandas DataFrame\n", "heart_data = pd.read_csv('/content/heart.csv')"], "execution_count": 2, "outputs": [{"output_type": "error", "ename": "FileNotFoundError", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-2-01c784c633dd>\u001b[0m in \u001b[0;36m<cell line: 2>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m# loading the csv data to a Pandas DataFrame\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mheart_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'/content/heart.csv'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/util/_decorators.py\u001b[0m in \u001b[0;36mwrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    209\u001b[0m                 \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    210\u001b[0m                     \u001b[0mkwargs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnew_arg_name\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnew_arg_value\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 211\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    212\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    213\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mcast\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mF\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/util/_decorators.py\u001b[0m in \u001b[0;36mwrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    329\u001b[0m                     \u001b[0mstacklevel\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mfind_stack_level\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    330\u001b[0m                 )\n\u001b[0;32m--> 331\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    332\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    333\u001b[0m         \u001b[0;31m# error: \"Callable[[VarArg(Any), KwArg(Any)], Any]\" has no\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, squeeze, prefix, mangle_dupe_cols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, error_bad_lines, warn_bad_lines, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options)\u001b[0m\n\u001b[1;32m    948\u001b[0m     \u001b[0mkwds\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkwds_defaults\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    949\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 950\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0m_read\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    951\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    952\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    603\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    604\u001b[0m     \u001b[0;31m# Create the parser.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 605\u001b[0;31m     \u001b[0mparser\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTextFileReader\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    606\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    607\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mchunksize\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0miterator\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m   1440\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1441\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhandles\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mIOHandles\u001b[0m \u001b[0;34m|\u001b[0m \u001b[0;32mNone\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1442\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_make_engine\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1443\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1444\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mclose\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m_make_engine\u001b[0;34m(self, f, engine)\u001b[0m\n\u001b[1;32m   1733\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0;34m\"b\"\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mmode\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1734\u001b[0m                     \u001b[0mmode\u001b[0m \u001b[0;34m+=\u001b[0m \u001b[0;34m\"b\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1735\u001b[0;31m             self.handles = get_handle(\n\u001b[0m\u001b[1;32m   1736\u001b[0m                 \u001b[0mf\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1737\u001b[0m                 \u001b[0mmode\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/io/common.py\u001b[0m in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    854\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mioargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mencoding\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;34m\"b\"\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mioargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmode\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    855\u001b[0m             \u001b[0;31m# Encoding\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 856\u001b[0;31m             handle = open(\n\u001b[0m\u001b[1;32m    857\u001b[0m                 \u001b[0mhandle\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    858\u001b[0m                 \u001b[0mioargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmode\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/content/heart.csv'"]}]}, {"cell_type": "code", "metadata": {"id": "M8dQxSTqriWD"}, "source": ["# print first 5 rows of the dataset\n", "heart_data.head()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "Fx_aCZDgrqdR"}, "source": ["# print last 5 rows of the dataset\n", "heart_data.tail()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "8nX1tIzbrz0u"}, "source": ["# number of rows and columns in the dataset\n", "heart_data.shape"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "7_xTcw1Sr6aJ"}, "source": ["# getting some info about the data\n", "heart_data.info()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "GjHtW31rsGlb"}, "source": ["# checking for missing values\n", "heart_data.isnull().sum()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "OHmcP7DJsSEP"}, "source": ["# statistical measures about the data\n", "heart_data.describe()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "4InaOSIUsfWP"}, "source": ["# checking the distribution of Target Variable\n", "heart_data['target'].value_counts()"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "aSOBu4qDtJy5"}, "source": ["1 --> Defective Heart\n", "\n", "0 --> <PERSON><PERSON> Heart"]}, {"cell_type": "markdown", "metadata": {"id": "tW8i4igjtPRC"}, "source": ["Splitting the Features and Target"]}, {"cell_type": "code", "metadata": {"id": "Q6yfbswrs7m3"}, "source": ["X = heart_data.drop(columns='target', axis=1)\n", "Y = heart_data['target']"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "XJoCp4ZKtpZy"}, "source": ["print(X)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "nukuj-YItq1w"}, "source": ["print(Y)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "_EcjSE3Et18n"}, "source": ["Splitting the Data into Training data & Test Data"]}, {"cell_type": "code", "metadata": {"id": "a-UUfRUxtuga"}, "source": ["X_train, X_test, Y_train, Y_test = train_test_split(X, Y, test_size=0.2, stratify=Y, random_state=2)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "x7PrjC6zuf6X"}, "source": ["print(X.shape, X_train.shape, X_test.shape)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "beSkZmpVuvn9"}, "source": ["Model Training"]}, {"cell_type": "markdown", "metadata": {"id": "gi2NOWZjuxzw"}, "source": ["Logistic Regression"]}, {"cell_type": "code", "metadata": {"id": "4-Md74FYuqNL"}, "source": ["model = LogisticRegression()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "kCdHYxGUu7XD"}, "source": ["# training the LogisticRegression model with Training data\n", "model.fit(X_train, Y_train)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "ZYIw8Gi9vXfU"}, "source": ["Model Evaluation"]}, {"cell_type": "markdown", "metadata": {"id": "wmxAekfZvZa9"}, "source": ["Accuracy Score"]}, {"cell_type": "code", "metadata": {"id": "g19JaUTMvPKy"}, "source": ["# accuracy on training data\n", "X_train_prediction = model.predict(X_train)\n", "training_data_accuracy = accuracy_score(X_train_prediction, Y_train)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "uQBZvBh8v7R_"}, "source": ["print('Accuracy on Training data : ', training_data_accuracy)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "mDONDJdlwBIO"}, "source": ["# accuracy on test data\n", "X_test_prediction = model.predict(X_test)\n", "test_data_accuracy = accuracy_score(X_test_prediction, Y_test)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "_MBS-OqdwYpf"}, "source": ["print('Accuracy on Test data : ', test_data_accuracy)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "jIruVh3Qwq0e"}, "source": ["Building a Predictive System"]}, {"cell_type": "code", "metadata": {"id": "9ercruC9wb4C"}, "source": ["input_data = (62,0,0,140,268,0,0,160,0,3.6,0,2,2)\n", "\n", "# change the input data to a numpy array\n", "input_data_as_numpy_array= np.asarray(input_data)\n", "\n", "# reshape the numpy array as we are predicting for only on instance\n", "input_data_reshaped = input_data_as_numpy_array.reshape(1,-1)\n", "\n", "prediction = model.predict(input_data_reshaped)\n", "print(prediction)\n", "\n", "if (prediction[0]== 0):\n", "  print('The Person does not have a Heart Disease')\n", "else:\n", "  print('The Person has Heart Disease')"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "FCHCMHpshHU4"}, "source": ["Saving the trained model"]}, {"cell_type": "code", "metadata": {"id": "cdmTOR4MhHCB"}, "source": ["import pickle"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "4gN09lokhKuZ"}, "source": ["filename = 'heart_disease_model.sav'\n", "pickle.dump(model, open(filename, 'wb'))"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "IKW4D5CqhP5X"}, "source": ["# loading the saved model\n", "loaded_model = pickle.load(open('heart_disease_model.sav', 'rb'))"], "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["for column in X.columns:\n", "  print(column)"], "metadata": {"id": "1SiD-oDpPSxY"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "A23YZI2tPaFk"}, "execution_count": null, "outputs": []}]}